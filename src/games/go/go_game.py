import copy

import numpy as np

from games.go.bfs_board import BFSGoBoard
from games.go.go_board import GoBoard, Move


class GoGame:
    def __init__(self, shape):
        self.board = BFSGoBoard(shape)
        self.history: set[int] = {hash(self.board)} # With positional superko we only track the state of the board.
        self.actions: list[Move] = []
        self.current_player = 1

        self.score = None

    def make_move(self, move: Move):
        if move.color != self.current_player:
            raise ValueError("Invalid move: Not current player's turn.")
        if self.score is not None:
            raise ValueError("Invalid move: Game already over.")
        if move.location is None: # Pass move
            if len(self.actions) > 0 and self.actions[-1].location is None:
                self.score = self.board.score()
            self.actions.append(move)
            self.current_player = -self.current_player
            return

        self.board.make_move(move)
        if self.board in self.history:
            raise ValueError("Invalid move: Board state already reached.")
        self.actions.append(move)
        self.history.add(hash(self.board))
        self.current_player = -self.current_player

    def legal_actions(self) -> np.ndarray:
        empty_locations = self.board.get_empty_locations().flatten()
        pass_action = np.array([True])
        return np.concatenate((empty_locations, pass_action))

    def __str__(self):
        return f"Current player: {self.current_player}\nScore: {self.score}\n{self.board}"

    def __hash__(self):
        # Basically, just add everything and have current board be double.
        return hash(self.board) + sum(self.history) # Not sure if this is a great hash function.


if __name__ == "__main__":
    game = GoGame((4,4))
    while True:
        try:
            loc_str = input("Enter move (row col): ")
            if loc_str.lower() == 'pass':
                move_to_make = Move(location=None, color=game.current_player)
            else:
                loc = tuple(map(int, loc_str.split()))
                move_to_make = Move(location=loc, color=game.current_player)
            game.make_move(move_to_make)
            print(game)
        except ValueError as e:
            print(e)
