import numpy as np

from go_board import GoBoard, Move

class BFSGoBoard(GoBoard):
    def make_move(self, move: Move):
        move_location, color = move.location, move.color
        if self.board[move_location] != 0:
            raise ValueError("Invalid move: Stone already placed at this location.")

        self._place_stone(move)

        # Remove what's needed
        for neighbor in self._get_neighbors(move_location):
            if self.board[neighbor] == -color:
                self._remove_if_needed(self.board, neighbor)
        self._remove_if_needed(self.board, move_location)

    def _remove_if_needed(self, board, location):
        """Removes the group at the given location if it has no liberties."""
        color = board[location]
        if board[location] == 0:
            return None

        # Basic BFS to look for liberties.
        visited = set()
        queue = [location]

        while queue:
            current = queue.pop(0)
            visited.add(current)
            neighbors = self._get_neighbors(current)
            for neighbor in neighbors:
                if board[neighbor] == 0:
                    return None
                if board[neighbor] == color and neighbor not in visited:
                    queue.append(neighbor)

        # If we reach this point, the group has no liberties, so remove it
        for cell in visited:
            self._remove_stone(cell)
        return None
