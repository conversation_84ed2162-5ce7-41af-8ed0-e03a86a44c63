# src/search/base_env.py

from abc import ABC, abstractmethod
from typing import Any, List, Tuple, Optional, TypeVar, Generic

# Generic type for actions, allowing different games to use different action representations
ActionType = TypeVar('ActionType')

# Forward declaration for StateType to be used within BaseGameState itself
StateType = TypeVar('StateType', bound='BaseGameState')

class BaseGameState(ABC, Generic[ActionType]):
    """
    Abstract base class for a game state.
    MCTS algorithms typically operate on these state objects.
    It's generic over ActionType.
    """

    @abstractmethod
    def get_current_player(self) -> int:
        """
        Returns the identifier of the current player to move.
        A common convention is 1 for the first player, -1 for the second.
        """
        pass

    @abstractmethod
    def get_legal_actions(self) -> List[ActionType]:
        """
        Returns a list of all legal actions available from this state
        for the current player.
        """
        pass

    @abstractmethod
    def play(self: StateType, action: ActionType) -> StateType:
        """
        Applies an action to the current state and returns the new game state.
        This method should ideally return a *new* instance of the game state,
        leaving the original state unchanged (promoting a functional approach).
        """
        pass

    @abstractmethod
    def is_terminal(self) -> bool:
        """
        Checks if the current game state is a terminal state (i.e., game over).
        """
        pass

    @abstractmethod
    def get_game_outcome(self) -> Optional[float]:
        """
        Returns the outcome of the game if the state is terminal.
        Convention:
            - 1.0 if the first player (e.g., player 1) wins.
            - -1.0 if the second player (e.g., player -1) wins (i.e., player 1 loses).
            - 0.0 for a draw.
            - Returns None if the state is not terminal.
        The outcome should be from a consistent perspective (e.g., always player 1's).
        The MCTS will then adjust this based on whose turn it was at a particular node.
        """
        pass

    @abstractmethod
    def __hash__(self) -> int:
        """
        Returns a hash value for the game state.
        Essential for use in transposition tables (caching visited states).
        """
        pass

    @abstractmethod
    def __eq__(self, other: object) -> bool:
        """
        Compares this game state with another object for equality.
        """
        pass

    def __str__(self) -> str:
        """
        Returns a string representation of the game state.
        Helpful for debugging and visualization.
        Defaults to the object's default string representation if not overridden.
        """
        return super().__str__()


class BaseEnv(ABC, Generic[ActionType, StateType]):
    """
    Abstract base class for a game environment.
    This class manages the game's lifecycle, provides initial states,
    and can offer information about action/observation spaces.
    It's generic over ActionType and StateType (which is bound by BaseGameState).
    """

    @abstractmethod
    def reset(self) -> StateType:
        """
        Resets the environment to an initial game state and returns that state.
        """
        pass

    @abstractmethod
    def get_action_space_size(self) -> int:
        """
        Returns the total number of distinct actions possible in the game.
        This is often useful for defining the output layer size of a policy network.
        """
        pass
