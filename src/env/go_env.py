# src/games/go/go_env.py

import copy
from typing import List, Tuple, Optional, Any
import numpy as np

# Assuming base_env.py is in src.env
from src.env.env import BaseEnv, BaseGameState, ActionType as BaseActionType

# Assuming go_game.py and go_board.py are in src.games.go
from src.games.go.go_game import GoGame
from src.games.go.go_board import Move  # PositionState might also be needed if not directly in GoGame

# Define the specific ActionType for Go
GoAction = Optional[Tuple[int, int]]  # (row, col) or None for pass


class GoGameState(BaseGameState[GoAction]):
    """
    Represents a specific state in a Go game, designed to work with MCTS.
    It wraps a GoGame instance.
    """

    def __init__(self, go_game_instance: GoGame, board_shape: Tuple[int, int]):
        self._go_game: GoGame = go_game_instance
        self._board_shape: Tuple[int, int] = board_shape
        # Precompute hash for efficiency, assuming GoGame state is fixed upon GoGameState creation
        self._hash_val: int = self._compute_hash()

    def _compute_hash(self) -> int:
        """
        Computes a hash for the current game state (board + current player).
        This is for MCTS transposition tables.
        The GoGame's internal history handles superko rules.
        """
        # hash(self._go_game.board) uses the Zobrist hash from BFSGoBoard
        board_hash = hash(self._go_game.board)
        player_hash = hash(self._go_game.current_player)
        return board_hash ^ player_hash

    def get_current_player(self) -> int:
        """Returns 1 for Black, -1 for White."""
        return self._go_game.current_player

    def get_legal_actions(self) -> List[GoAction]:
        """
        Converts the GoGame's legal_actions numpy array into a list of GoAction.
        """
        legal_actions_mask: np.ndarray = self._go_game.legal_actions()
        legal_go_actions: List[GoAction] = []

        board_width = self._board_shape[1]

        for i in range(len(legal_actions_mask) - 1):  # Iterate through board positions
            if legal_actions_mask[i]:
                row = i // board_width
                col = i % board_width
                legal_go_actions.append((row, col))

        if legal_actions_mask[-1]:  # Check for pass action
            legal_go_actions.append(None)

        return legal_go_actions

    def play(self, action: GoAction) -> 'GoGameState':
        """
        Applies an action and returns a *new* GoGameState.
        The original GoGame instance within this GoGameState is not modified.
        """
        # Create a deep copy of the internal GoGame to maintain immutability for this state instance
        new_go_game_instance = copy.deepcopy(self._go_game)

        # Convert GoAction to Move object
        move_obj = Move(location=action, color=new_go_game_instance.current_player)

        try:
            new_go_game_instance.make_move(move_obj)
        except ValueError as e:
            # This can happen if the action, though listed as legal by the simplified
            # GoGame.legal_actions(), turns out to be illegal due to superko
            # or other rules checked deeply in make_move.
            # MCTS should ideally handle this by not exploring such paths further
            # or by having a more robust legal_actions generator.
            # For now, re-raise or handle as appropriate for your MCTS.
            # print(f"Warning/Error during play in GoGameState: {e} for action {action}")
            raise e  # Or return a state indicating an error/loss for the current player

        return GoGameState(new_go_game_instance, self._board_shape)

    def is_terminal(self) -> bool:
        """Checks if the game has ended (score is calculated)."""
        return self._go_game.score is not None

    def get_game_outcome(self) -> Optional[float]:
        """
        Returns the game outcome from Player 1's (Black's) perspective.
        1.0 for Black win, -1.0 for White win, 0.0 for draw.
        None if not terminal.
        """
        if not self.is_terminal():
            return None

        score = self._go_game.score
        if score is None:  # Should not happen if is_terminal is true
            return 0.0  # Or raise error

        if score > 0:
            return 1.0  # Black (Player 1) wins
        elif score < 0:
            return -1.0  # White (Player -1) wins, so Player 1 loses
        else:
            return 0.0  # Draw

    def __hash__(self) -> int:
        return self._hash_val

    def __eq__(self, other: object) -> bool:
        if not isinstance(other, GoGameState):
            return False
        # Equality based on board state and current player
        return hash(self._go_game.board) == hash(other._go_game.board) and \
            self._go_game.current_player == other._go_game.current_player

    def __str__(self) -> str:
        return str(self._go_game)

    # Optional: For neural network input
    def get_observation(self) -> np.ndarray:
        """
        Returns a representation of the board state.
        Could be extended to include current player, history, etc.
        """
        # Example: return a copy of the board
        # In a real scenario, this might be multiple feature planes
        return self._go_game.board.board.copy()


class GoEnv(BaseEnv[GoAction, GoGameState]):
    """
    Go Environment that conforms to the BaseEnv interface.
    """

    def __init__(self, board_size: Tuple[int, int] = (19, 19)):
        if not (isinstance(board_size, tuple) and len(board_size) == 2 and
                isinstance(board_size[0], int) and isinstance(board_size[1], int)):
            raise ValueError("board_size must be a tuple of two integers, e.g., (19, 19)")
        self.board_size: Tuple[int, int] = board_size

    def reset(self) -> GoGameState:
        """
        Resets the environment to an initial game state.
        """
        new_internal_go_game = GoGame(shape=self.board_size)
        return GoGameState(new_internal_go_game, self.board_size)

    def get_action_space_size(self) -> int:
        """
        Returns the total number of distinct actions possible in Go.
        (board_width * board_height for placements + 1 for pass).
        """
        return (self.board_size[0] * self.board_size[1]) + 1

    # Optional: If you need to get the observation shape for a NN
    def get_observation_shape(self) -> Tuple[int, ...]:
        """
        Returns the shape of the observation (e.g., (board_height, board_width)).
        This might need to be more complex if you use multiple feature planes.
        """
        # Simple case: just the board dimensions
        return self.board_size
        # If observation includes player plane: (2, height, width) or (height, width, 2)


if __name__ == '__main__':
    # Example Usage
    go_env = GoEnv(board_size=(5, 5))
    current_go_state: GoGameState = go_env.reset()

    print("--- Initial State ---")
    print(current_go_state)
    print(f"Current Player: {current_go_state.get_current_player()}")
    print(f"Is Terminal: {current_go_state.is_terminal()}")
    print(f"Hashed State: {hash(current_go_state)}")

    legal_actions = current_go_state.get_legal_actions()
    print(f"Legal Actions ({len(legal_actions)}): {legal_actions[:5]}... ")  # Print first 5

    if legal_actions:
        action_to_take = legal_actions[0]  # Take the first legal action
        print(f"\n--- Taking Action: {action_to_take} ---")
        current_go_state = current_go_state.play(action_to_take)
        print(current_go_state)
        print(f"Current Player: {current_go_state.get_current_player()}")
        print(f"Is Terminal: {current_go_state.is_terminal()}")
        print(f"Hashed State: {hash(current_go_state)}")

        # Example of passing
        pass_action: GoAction = None
        if pass_action in current_go_state.get_legal_actions():
            print(f"\n--- Taking Action: Pass ---")
            current_go_state = current_go_state.play(pass_action)
            print(current_go_state)
            print(f"Current Player: {current_go_state.get_current_player()}")

            # Second pass to end game
            if pass_action in current_go_state.get_legal_actions():
                print(f"\n--- Taking Action: Pass (2nd) ---")
                current_go_state = current_go_state.play(pass_action)
                print(current_go_state)
                print(f"Is Terminal: {current_go_state.is_terminal()}")
                if current_go_state.is_terminal():
                    print(f"Game Outcome (for P1/Black): {current_go_state.get_game_outcome()}")

    print(f"\nAction space size: {go_env.get_action_space_size()}")
    print(f"Observation shape (basic): {go_env.get_observation_shape()}")
